package com.cw.service.context;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.cw.config.satoken.MchStpUtil;
import com.cw.utils.JwtUtils;
import com.cw.utils.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Locale;


/**
 * 根据 token 获取访问用户的信息
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/3/14 01:39
 **/
@Slf4j
public class MchContext {
    public static final String LANG = "Language";  //对应客户端要求的多语言

    public static String getOtaHeaderValue(String key) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {//ota接口进来这里可能为null
            HttpServletRequest request = attributes.getRequest();
            String value = request.getHeader(key);
            if (value != null) {
                return value;
            }
        }
        return StrUtil.EMPTY;
    }


    /**
     * 返回当前访问用户的USERID
     *
     * @return
     */
    public static String getCurrentUserId() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (servletRequestAttributes == null) {  //先处理下夜审过账不传hotelid的情况
            return SystemUtil.SYSUSERID;
        }
        String token = MchStpUtil.getTokenValue();
        if (null == token) {  // XXX 先处理下夜审过账不传hotelid的情况 
            return SystemUtil.SYSUSERID;
        }
        Object userid = JwtUtils.getClaimsFromToken(token).get(JwtUtils.LOGIN_USERID_KEY);
        return userid == null ? "" : userid + "";
    }

    /**
     * 返回当前访问用户的hotelid
     *
     * @return
     */
	public static String getCurrentHotelId() {
//		String debugHotelid = System.getProperty("debughotelid");
//		if (null != debugHotelid) {
//			log.warn("来自调试指定Hotelid:{}", debugHotelid);
//			return debugHotelid;
//		}
		String token = MchStpUtil.getTokenValue();
		Object hotelid = JwtUtils.getClaimsFromToken(token).get(JwtUtils.LOGIN_HOTELID_KEY);
		return hotelid == null ? "" : hotelid + "";
	}



    /**
     * 返回当前访问几天用户的登录手机号
     *
     * @return
     */
    public static String getCurrentGroupMobile() {
        //todo OTA渠道是否调用
        String token = MchStpUtil.getTokenValue();
        Object groupMobile = JwtUtils.getClaimsFromToken(token).get(JwtUtils.LOGIN_GROUP_MOBILE_KEY);
        return groupMobile == null ? "" : groupMobile + "";
    }

    /**
     * 返回当前请求来源IP
     * @return
     */
    public static String getRequestIp() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            return ServletUtil.getClientIP(request);
        }
        return StrUtil.EMPTY;
    }

    /**
     * 返回当前请求的语言类型
     * 默认返回zh
     *
     * @return
     */
    public static String getCurrentLang() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {//ota接口进来这里可能为null
            HttpServletRequest request = attributes.getRequest();
            String lang = request.getHeader(LANG);
            if (lang != null) {
                return lang;
            }
        }
        return Locale.CHINESE.getLanguage();//zh
    }


}
