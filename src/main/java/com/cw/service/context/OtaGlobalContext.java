package com.cw.service.context;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.cw.utils.JwtUtils;
import com.cw.utils.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Locale;


/**
 * 返回ota 全局上下文信息
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/3/14 01:39
 **/
@Slf4j
public class OtaGlobalContext {
    public static final String LANG = "Language";  //对应客户端要求的多语言

    public static String getOtaHeaderValue(String key) {
//		String debugHotelid = System.getProperty("debughotelid");
//		if (null != debugHotelid) {
//			log.warn("来自调试指定Hotelid:{}", debugHotelid);
//			return debugHotelid;
//		}
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {//ota接口进来这里可能为null
            HttpServletRequest request = attributes.getRequest();
            String value = request.getHeader(key);
            if (value != null) {
                return value;
            }
        }
        return StrUtil.EMPTY;
    }

    /**
     * 返回当前访问用户的USERID
     *
     * @return
     */
    public static String getCurrentUserId() {
        return getOtaHeaderValue(OtaHeader.APPID);
    }

    /**
     * 返回当前访问用户的hotelid
     *
     * @return
     */
    public static String getCurrentHotelId() {
        return getOtaHeaderValue(OtaHeader.HOTELID);
    }

    public static HttpServletRequest getRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            return attributes.getRequest();
        }
        return null;
    }


    /**
     * 返回当前请求来源IP
     *
     * @return
     */
    public static String getRequestIp() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            return ServletUtil.getClientIP(request);
        }
        return StrUtil.EMPTY;
    }

    public static class OtaHeader {
        public static final String APPID = "AppId";  // 分配出去的appid
        public static final String HOTELID = "HotelId";  //对应客户端要求的多语言
        public static final String SIGN = "Sign";// 签名
        public static final String STAMP = "Stamp"; // 时间戳
        public static final String NONCE = "Nonce"; // 随机数
    }


}
