package com.cw.controller.app;

import com.cw.exception.DefinedException;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.CommonDelReq;
import com.cw.pojo.dto.common.req.CommonQueryPageReq;
import com.cw.pojo.dto.common.req.CommonQueryReq;
import com.cw.pojo.dto.common.req.Common_Price_Req;
import com.cw.pojo.dto.common.res.Common_Price_Res;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.pms.req.reservation.*;
import com.cw.pojo.dto.pms.req.room.RoomSearchReq;
import com.cw.pojo.dto.pms.req.standardgroup.QueryStandardGroupReq;
import com.cw.pojo.dto.pms.req.standardgroup.StandardGroupReq;
import com.cw.pojo.dto.pms.res.profile.ProfileRes;
import com.cw.pojo.dto.pms.res.rate.RoomRateDetailRes;
import com.cw.pojo.dto.pms.res.reservation.*;
import com.cw.pojo.dto.pms.res.room.RoomListRes;
import com.cw.pojo.dto.pms.res.standardgroup.StandardGroupRes;
import com.cw.service.config.reservation.ReservationService;
import com.cw.service.config.reservation.RsInfoService;
import com.cw.service.config.reservation.impl.CheckOutServiceImpl;
import com.cw.service.context.GlobalContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @Classname ReservationController
 * @Description 预订控制器
 * @Date 2024-03-27 20:48
 * <AUTHOR> sancho.shen
 */
@RestController
@RequestMapping(value = "/app-api/reservation")
@Api(tags = "前台接待-预订")
public class ReservationController {

    @Autowired
    RsInfoService rsInfoService;

    @Autowired
    CheckOutServiceImpl checkOutService;


    @Resource
    private ReservationService reservationService;


    @ApiOperation(value = "根据房型查询房价")
    @PostMapping(value = "/find-room-rate")
    public ResultJson<RoomRateDetailRes> queryRoomRate(@Valid @RequestBody QueryRateReq queryRateReq) {
        return ResultJson.ok().data(reservationService.queryRoomRate(queryRateReq));
    }


    @ApiOperation(value = "新建散客预订")
    @PostMapping(value = "/add")
    public ResultJson<RsOrderResult> addReservationTourist(@Valid @RequestBody ReservationReq reservationReq) throws DefinedException{
        RsOrderResult orderResult = reservationService.addReservationTourist(reservationReq);
        return ResultJson.ok().data(orderResult);
    }

    @ApiOperation(value = "新建团队预订-批量创建")
    @PostMapping(value = "/batchadd")
    public ResultJson<RsOrderResult> addReservationTourist(@Valid @RequestBody BatchCreateRsReq reservationReq) throws DefinedException {
        RsOrderResult orderResult = reservationService.addGroupReservation(reservationReq);
        return ResultJson.ok().data(orderResult);
    }

    @ApiOperation(value = "修改预订")
    @PostMapping(value = "/update")
    public ResultJson updateReservation(@Valid @RequestBody UpdateReservationReq updateReservationReq)throws DefinedException {
        reservationService.updateReservation(updateReservationReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "查询预订列表")
    @PostMapping(value = "/list")
    public ResultJson<ReservationListRes> listReservationPage(@Valid @RequestBody QueryReservationListReq queryReservationListReq) {
        ReservationListRes reservationListRes = reservationService.listReservationPage(queryReservationListReq);
        return ResultJson.ok().data(reservationListRes);
    }

    @ApiOperation(value = "查询可转账订单列表")
    @PostMapping(value = "/transfer-list")
    public ResultJson<ReservationListRes> listReservationPage(@Valid @RequestBody QueryRsTransferPageReq queryReservationListReq) {
        ReservationListRes reservationListRes = reservationService.queryTransRes(queryReservationListReq);
        return ResultJson.ok().data(reservationListRes);
    }




    @ApiOperation(value = "自助机单房间查询")
    @PostMapping(value = "/query_room_rs")
    public ResultJson<ReservationRes> queryRoomRs(@Valid @RequestBody QueryReservationReq req) {
        ReservationRes reservationListRes = reservationService.queryRoomRs(req);
        return ResultJson.ok().data(reservationListRes);
    }

    @ApiOperation(value = "通过预订关联号查询关联的预订信息")
    @PostMapping(value = "/relation")
    public ResultJson<List<ReservationRes>> listReservation(@RequestParam String relationNumber) {
        List<ReservationRes> reservationRes = reservationService.listReservation(relationNumber);
        return ResultJson.ok().data(reservationRes);
    }


    @ApiOperation(value = "客房预订订单详情")
    @PostMapping(value = "/load")
    public ResultJson<RoomRsViewRes> queryReservation(@Valid @RequestBody RoomRsLoadReq req) {
        RoomRsViewRes res = reservationService.loadRoomRsView(req);
        return ResultJson.ok().data(res);
    }


    @ApiOperation(value = "查询某个预订")
    @PostMapping(value = "/find")
    public ResultJson<List<ReservationRes>> queryReservation(@Valid @RequestBody QueryReservationReq queryReservationReq) {
        List<ReservationRes> res = reservationService.queryReservation(queryReservationReq);
        return ResultJson.ok().data(res);
    }

    @ApiOperation(value = "删除预订")
    @PostMapping(value = "/delete")
    public ResultJson deleteReservation(@RequestBody CommonDelReq req) {
        reservationService.deleteReservation(req.getId());
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "入住")
    @PostMapping(value = "/checkin")
    public ResultJson checkin(@Valid @RequestBody CheckinReq checkinReq) throws DefinedException {
        reservationService.checkin(checkinReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "取消订单")
    @PostMapping(value = "/cancel")
    public ResultJson cancel(@Valid @RequestBody CancelReq cancelReq) throws DefinedException {
        reservationService.cancel(cancelReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "取消入住")
    @PostMapping(value = "/cancelcheckin")
    public ResultJson cancelcheckin(@Valid @RequestBody CancelReq cancelReq) throws DefinedException {
        reservationService.cancelCheckin(cancelReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "恢复订单")
    @PostMapping(value = "/reactive")
    public ResultJson reactive(@Valid @RequestBody RsOpReq req) throws DefinedException {
        reservationService.internalReactiveReservation(GlobalContext.getCurrentHotelId(), req.getReservationNumber(), GlobalContext.getCurrentUserId());
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperation(value = "换房")
    @PostMapping(value = "/room-move")
    public ResultJson roomMove(@Valid @RequestBody RoomMoveReq roomMoveReq)throws DefinedException {
        reservationService.roomMove(roomMoveReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "退房")
    @PostMapping(value = "/checkout")
    public ResultJson checkout(@Valid @RequestBody CheckoutReq checkoutReq) throws DefinedException {
        reservationService.checkout(checkoutReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "一键批量退房")
    @PostMapping(value = "/batchcheckout")
    public ResultJson<Common_response> batchCheckout(@Valid @RequestBody BatchCheckoutReq checkoutReq) throws DefinedException {
        Common_response msg = reservationService.batchCheckout(checkoutReq);
        return ResultJson.ok().data(msg);
    }

    @ApiOperation(value = "同住")
    @PostMapping(value = "/add-share")
    public ResultJson addShare(@Valid @RequestBody AddShareReq addShareReq) {
        reservationService.addShare(addShareReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "延住")
    @PostMapping(value = "/extend")
    public ResultJson extend(@Valid @RequestBody ExtendReq extendReq)throws DefinedException {
        reservationService.extend(extendReq);
        return ResultJson.ok().data(null);
    }


    @ApiOperation(value = "查询可卖房与可卖房价")
    @PostMapping(value = "/rate-query")
    public ResultJson<RateQueryRes> rateQuery(@Valid @RequestBody RateQueryReq rateQueryReq) {
        RateQueryRes res = reservationService.rateQuery(rateQueryReq);
        return ResultJson.ok().data(res);
    }

    @ApiOperation(value = "分房接口/若是多个房的订单则做相应的拆分预订的操作")
    @PostMapping(value = "/assign-room")
    public ResultJson<AssignReservationRes> assignRoom(@Valid @RequestBody AssignRoomReq assignRoomReq) {
        AssignReservationRes res = reservationService.assignRoom(assignRoomReq);
        return ResultJson.ok().data(res);
    }

    @ApiOperation(value = "批量分配房间OR批量取消分房")
    @PostMapping(value = "/batch-assign-room")
    public ResultJson<BatchAssginRoomResult> batchAssignRoom(@Valid @RequestBody BatchAssignRoomReq batchAssignRoomReq) throws DefinedException {
        BatchAssginRoomResult res = reservationService.batchAssignRoom(batchAssignRoomReq);
        return ResultJson.ok().data(res);
    }


    //@ApiOperation(value = "创建团队订单")
    //@PostMapping(value = "/add-group")
    //public ResultJson addReservationGroup(@Valid @RequestBody StandardGroupReq standardGroupReq) {
    //    reservationService.addReservationGroup(standardGroupReq);
    //    return ResultJson.ok().data(null);
    //}

    @ApiOperation(value = "查询团队预订列表")
    @PostMapping(value = "/list-group")  //List<StandardGroupRes>
    public ResultJson<GroupRsListRes> listReservationGroup(@Valid @RequestBody QueryStandardGroupReq queryStandardGroupReq) {
        GroupRsListRes rsListRes = reservationService.listReservationGroup(queryStandardGroupReq);
        return ResultJson.ok().data(rsListRes);
    }

    @ApiOperation(value = "房价计算")
    @PostMapping(value = "/calcprice")
    public ResultJson<Common_Price_Res> calcprice(@RequestBody Common_Price_Req req) {
        Common_Price_Res res = rsInfoService.calcPrice(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperation(value = "拉取包价信息")
    @PostMapping(value = "/pkg-query")
    public ResultJson<PkgQueryRes> ratequery(@RequestBody CommonQueryReq req) {
        PkgQueryRes res = rsInfoService.queryPkg(req);
        return ResultJson.ok().data(res);
    }


    @ApiOperation(value = "查询可预订房间列表")
    @PostMapping(value = "/avlroom-list")
    public ResultJson<RoomListRes> getAvlRoomPage(@RequestBody RoomSearchReq roomSearchReq) {
        RoomListRes res = rsInfoService.getAvlRoomPage(roomSearchReq);
        return ResultJson.ok().data(res);
    }

    @ApiOperation(value = "分房预校验")
    @PostMapping(value = "/validateRoomOcc")
    public ResultJson validateRoomOcc(@RequestBody CommonQueryReq req) {
        Common_response res = rsInfoService.validateRoomOcc(req);
        return ResultJson.ok();
    }

    @ApiOperation(value = "查询房间当前占用预订")
    @PostMapping(value = "/getRoomOcc")
    public ResultJson<ReservationRes> getRoomOcc(@RequestBody CommonQueryReq req) {
        ReservationRes res = rsInfoService.queryOccRoom(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperation(value = "校验可卖房")
    @PostMapping(value = "/checkAvl")
    public ResultJson<Common_response> checkAvl(@RequestBody CommonQueryReq req) {
        Common_response res = rsInfoService.checkAvl(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperation(value = "档案登记")
    @PostMapping(value = "/registration")
    public ResultJson registration(@RequestBody RegistrationReq registrationReq) {
        reservationService.registration(registrationReq);
        return ResultJson.ok();
    }

    @ApiOperation(value = "拉取客房预订离店校验信息")
    @PostMapping(value = "/validateCheckout")
    public ResultJson<CheckoutCheckListRes> checkOutList(@RequestBody CheckoutCheckListReq req) {
        CheckoutCheckListRes res = checkOutService.checkList(req);

        return ResultJson.ok().data(res);
    }

    @ApiOperation(value = "提交离店操作-加收房费等")
    @PostMapping(value = "/checkoutOp")
    public ResultJson<Common_response> checkoutOp(@RequestBody CheckoutCheckListHandleReq req) {
        checkOutService.handleCheckList(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperation(value = "查询某个预订下的所有档案信息")
    @PostMapping(value = "/find-profile")
    public ResultJson<List<ProfileRes>> findProfile(@RequestParam String reservationNumber){
        List<ProfileRes> profileList = reservationService.findProfile(reservationNumber);
        return ResultJson.ok().data(profileList);
    }

    @ApiOperation(value = "订单出入团")
    @PostMapping(value = "/op-grouprelation")
    public ResultJson transferGroup(@Valid @RequestBody GroupTransferReq req) throws DefinedException {
        reservationService.transferGroup(req);
        return ResultJson.ok();
    }


    @ApiOperation(value = "团队预订搜索")
    @PostMapping(value = "/group-search")
    public ResultJson<GroupRoomSearchRes> groupSearch(@Valid @RequestBody GroupRoomSearchReq req) throws DefinedException {
        GroupRoomSearchRes res = rsInfoService.queryGroupRes(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperation(value = "待结账订单")
    @PostMapping(value = "/pending-checkout")
    public ResultJson pendingCheckOut(@Valid @RequestBody PendingCheckOutReq pendingCheckOutReq) throws DefinedException {
        reservationService.pendingCheckOut(pendingCheckOutReq);
        return ResultJson.ok().data(null);
    }

    @ApiOperation(value = "复制预订")
    @PostMapping(value = "/copy-reservation")
    public ResultJson<ReservationRes> groupSearch(@Valid @RequestBody CopyReservationReq req) throws DefinedException {
        RsOrderResult orderResult = reservationService.copyReservation(req);
        return ResultJson.ok().data(orderResult);
    }

    @ApiOperation(value = "撤销待结账")
    @PostMapping(value = "/cancel-pending")
    public ResultJson cancelpending(@Valid @RequestBody RsOpReq req) throws DefinedException {
        reservationService.cancelPending(GlobalContext.getCurrentHotelId(), req.getReservationNumber(), GlobalContext.getCurrentUserId());
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperation(value = "取消离店")
    @PostMapping(value = "/cancelcheckout")
    public ResultJson cancelchenckout(@Valid @RequestBody CancelReq cancelReq) throws DefinedException {
        reservationService.cancelCheckout(cancelReq);
        return ResultJson.ok().data(new Common_response());
    }
}
