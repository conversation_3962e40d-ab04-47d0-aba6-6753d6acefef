package com.cw.controller.ota;

import com.cw.core.CoreCrsV1;
import com.cw.pms.request.*;
import com.cw.pms.request.crsv1.CwCrsColPayV1Req;
import com.cw.pms.request.crsv1.CwCrsColRsSaveV1Req;
import com.cw.pms.request.crsv1.CwCrsRoomRsSaveV1Req;
import com.cw.pms.response.*;
import com.cw.pms.response.crsv1.CwCrsColPayV1Res;
import com.cw.pms.response.crsv1.CwCrsColRsSaveV1Res;
import com.cw.pms.response.crsv1.CwCrsRoomSaveV1Res;
import com.cw.pms.standard.PmsOtaStandard;
import com.cw.service.config.ota.PmsOtaApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/pmsopen/v1")
@Api(tags = "OTA对接服务接口")
@Slf4j
public class PmsApiController implements PmsOtaStandard {

    @Resource
    private PmsOtaApiService pmsOtaApiService;

    @Resource
    CoreCrsV1 coreCrsV1;

    @PostMapping("/query/area")
    @Override
    public CwAreaQueryRes queryArea(@RequestBody CwAreaQueryReq req) {
        CwAreaQueryRes res = pmsOtaApiService.queryArea(req);
        return res;
    }

    @PostMapping("/query/blocksku")
    @Override
    public CwBlockAllotmentQueryRes queryBlockAllotment(@RequestBody CwBlockAllotmentQueryReq req) {
        CwBlockAllotmentQueryRes res = pmsOtaApiService.queryBlockAllotment(req);
        return res;
    }

    @PostMapping("/query/channels")
    @Override
    public CwChannelQueryRes queryChannel(@RequestBody CwChannelQueryReq req) {
        CwChannelQueryRes res = pmsOtaApiService.queryChannel(req);
        return res;
    }

    @PostMapping("/query/country")
    @Override
    public CwCountryCodeRes queryCountryCode(@RequestBody CwCountryCodeReq req) {
        CwCountryCodeRes res = pmsOtaApiService.queryCountryCode(req);
        return res;
    }

    @PostMapping("/query/deptcode")
    @Override
    public CwDepartmentQueryRes queryDepartment(@RequestBody CwDepartmentQueryReq req) {
        CwDepartmentQueryRes res = pmsOtaApiService.queryDepartment(req);
        return res;
    }

    @PostMapping("/query/folios")
    @Override
    public CwAccountQueryRes queryAccount(@RequestBody CwAccountQueryReq req) {
        CwAccountQueryRes res = pmsOtaApiService.queryAccount(req);
        return res;
    }

    @PostMapping("/query/hotelcode")
    @Override
    public CwHotelCodeQueryRes queryHotelCode(@RequestBody CwHotelCodeQueryReq req) {
        CwHotelCodeQueryRes res = pmsOtaApiService.queryHotelCode(req);
        return res;
    }

    @PostMapping("/query/idtype")
    @Override
    public CwIDTypeQueryRes queryIDType(@RequestBody CwIDTypeQueryReq req) {
        CwIDTypeQueryRes res = pmsOtaApiService.queryIDType(req);
        return res;
    }

    @PostMapping("/query/payment")
    @Override
    public CwPaymentQueryRes queryPayment(@RequestBody CwPaymentQueryReq req) {
        CwPaymentQueryRes res = pmsOtaApiService.queryPayment(req);
        return res;
    }

    @PostMapping("/query/restype")
    @Override
    public CwResTypeQueryRes queryResType(@RequestBody CwResTypeQueryReq req) {
        CwResTypeQueryRes res = pmsOtaApiService.queryResType(req);
        return res;
    }

    @PostMapping("/query/roomtype")
    @Override
    public CwRoomTypeRes queryRoomType(@RequestBody CwRoomTypeReq req) {
        CwRoomTypeRes res = pmsOtaApiService.queryRoomType(req);
        return res;
    }

    @PostMapping("/query/sku")
    @Override
    public CwRoomInventoryRes queryRoomInventory(@RequestBody CwRoomInventoryReq req) {
        CwRoomInventoryRes res = pmsOtaApiService.queryRoomInventory(req);
        return res;
    }

    @PostMapping("/query/market")
    @Override
    public CwMarketQueryRes queryMarket(@RequestBody CwMarketQueryReq req) {
        CwMarketQueryRes res = pmsOtaApiService.queryMarket(req);
        return res;
    }

    @PostMapping("/query/sources")
    @Override
    public CwSourceQueryRes querySource(@RequestBody CwSourceQueryReq req) {
        CwSourceQueryRes res = pmsOtaApiService.querySource(req);
        return res;
    }

    @PostMapping("/query/receivable-account")
    @Override
    public CwQueryArAccountRes queryArAccount(@RequestBody CwQueryArAccountReq req) {
        CwQueryArAccountRes res = pmsOtaApiService.queryArAccount(req);
        return res;
    }

    @PostMapping("/query/room-order")
    @Override
    public CwQueryRoomOrderRes queryRoomOrder(@RequestBody CwQueryRoomOrderReq req) {
        CwQueryRoomOrderRes res = pmsOtaApiService.queryRoomOrder(req);
        return res;
    }

    @PostMapping("/block/savegrid")
    @Override
    public CwBlockAllotmentRes allotmentBlock(@RequestBody CwBlockAllotmentReq req) {
        CwBlockAllotmentRes res = pmsOtaApiService.allotmentBlock(req);
        return res;
    }

    @PostMapping("/block/saveblock")
    @Override
    public CwBlockRes saveBlock(@RequestBody CwBlockReq req) {
        CwBlockRes res = pmsOtaApiService.saveBlock(req);
        return res;
    }

    @PostMapping("/order/cancel")
    @Override
    public CwCancelRoomRes cancelRoom(@RequestBody CwCancelRoomReq req) {
        CwCancelRoomRes res = pmsOtaApiService.cancelRoom(req);
        return res;
    }

    @PostMapping("/order/save")
    @Override
    public CwSaveRoomRes saveRoom(@RequestBody CwSaveRoomReq req) {
        CwSaveRoomRes res = pmsOtaApiService.saveRoom(req);
        return res;
    }

    @PostMapping("/order/savecolrs")
    @Override
    public CwColRsRes saveColReservation(@RequestBody CwColRsReq req) {
        CwColRsRes res = pmsOtaApiService.saveColReservation(req);
        log.info("saveColReservation req:{}", req);
        return res;
    }

    @PostMapping("/order/cancelcolrs")
    @Override
    public CwPmsCommonRes cancelReservation(@RequestBody CwCancelColRsReq req) {
        CwPmsCommonRes res = pmsOtaApiService.cancelReservation(req);
        return res;
    }

    /**
     * DSPMS综合预订接口
     * 支持客房预订、餐饮预订、门票预订、导游预订等多种预订类型
     */
    @PostMapping("/dspms/savecolrs")
    @ApiOperation(value = "DSPMS综合预订", notes = "支持多种预订类型的综合预订接口")
    public CwCrsColRsSaveV1Res saveDspmsColReservation(@RequestBody CwCrsColRsSaveV1Req req) {
        log.info("saveDspmsColReservation req:{}", req);
        CwCrsColRsSaveV1Res res = pmsOtaApiService.saveDspmsColReservation(req);
        return res;
    }

    /**
     * DSPMS客房预订接口
     * 专门用于客房预订的简化接口
     */
    @PostMapping("/dspms/saveroom")
    @ApiOperation(value = "DSPMS客房预订", notes = "专门用于客房预订的简化接口")
    public CwCrsRoomSaveV1Res saveDspmsRoom(@RequestBody CwCrsRoomRsSaveV1Req req) {
        log.info("saveDspmsRoom req:{}", req);
        CwCrsRoomSaveV1Res res = pmsOtaApiService.saveDspmsRoom(req);
        return res;
    }

    @PostMapping("/order/ar-pay")
    @Override
    public CwPmsCommonRes arpay(@RequestBody CwOrderArPayReq req) {
        CwPmsCommonRes res = pmsOtaApiService.arpay(req);
        return res;
    }

    @PostMapping("/order/room-consume")
    @Override
    public CwOrderConsumptionRes roomConsumption(@RequestBody CwOrderConsumptionReq req) {
        CwOrderConsumptionRes res = pmsOtaApiService.roomConsumption(req);
        return res;
    }

    @PostMapping("/order/paycol")
    @Override
    public CwColPayRes orderPay(@RequestBody CwColPayReq req) {
        CwColPayRes res = pmsOtaApiService.orderPay(req);
        return res;
    }

    @PostMapping("/order/cancel-charge")
    @Override
    public CwPmsCommonRes cancelCharge(@RequestBody CwCancelChargeReq req) {
        CwPmsCommonRes res = pmsOtaApiService.cancelCharge(req);
        return res;
    }

    @Override
    @PostMapping("/order/querycol")
    public CwQueryColRes queryColRs(@RequestBody CwQueryColReq cwQueryColReq) {

        CwQueryColRes res = pmsOtaApiService.queryColRs(cwQueryColReq);
        return res;
    }

    @Override
    @PostMapping(value = "/order/checkin")
    public CwCheckInRes checkin(@RequestBody CwCheckInReq req) {
        CwCheckInRes res = pmsOtaApiService.checkin(req);
        return res;
    }

    @Override
    @PostMapping("/query/checkin")
    public CwQueryCheckinOrderRes queryCheckinOrder(@RequestBody CwQueryCheckinOrderReq req) {
        CwQueryCheckinOrderRes res = pmsOtaApiService.queryCheckinOrder(req);
        return res;
    }

    @Override
    @PostMapping("/query/hotelinfo")
    public CwQueryHotelInfoRes queryHotelInfo(@RequestBody CwQueryHotelInfoReq req) {
        CwQueryHotelInfoRes res = pmsOtaApiService.queryHotelInfo(req);
        return res;
    }

    @Override
    public CwCrsColRsSaveV1Res saveCrsColRsV1(CwCrsColRsSaveV1Req req) {
        return coreCrsV1.saveCrsColRsV1(req);
    }

    @Override
    public CwCrsRoomSaveV1Res saveCrsRoomRsV1(CwCrsRoomRsSaveV1Req req) {
        return coreCrsV1.saveCrsRoomRsV1(req);
    }

    @Override
    public CwCrsColPayV1Res crsColPayV1(CwCrsColPayV1Req req) {
        return coreCrsV1.crsColPayV1(req);
    }
}