package com.cw.core;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.entity.Colrs;
import com.cw.entity.Reservation;
import com.cw.mapper.common.DaoLocal;
import com.cw.pms.request.crsv1.CwCrsColPayV1Req;
import com.cw.pms.request.crsv1.CwCrsColRsSaveV1Req;
import com.cw.pms.request.crsv1.CwCrsRoomRsSaveV1Req;
import com.cw.pms.response.crsv1.CwCrsColPayV1Res;
import com.cw.pms.response.crsv1.CwCrsColRsSaveV1Res;
import com.cw.pms.response.crsv1.CwCrsRoomSaveV1Res;
import com.cw.pojo.dto.pms.res.reservation.RsOrderResult;
import com.cw.service.context.GlobalContext;
import com.cw.service.context.OtaGlobalContext;
import com.cw.utils.SystemUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/7/25 15:36
 **/
@Service
public class CoreCrsV1 {

    @Autowired
    DaoLocal<?> daoLocal;

    @Autowired
    private SeqNoService seqNoService;

    @Autowired
    private CoreRs coreRs;


    public CwCrsColRsSaveV1Res saveCrsColRsV1(CwCrsColRsSaveV1Req req) {
        boolean lnew = false;
        String hotelId = OtaGlobalContext.getCurrentHotelId();
        Colrs colrs = daoLocal.getObject("from Colrs c where c.crsno = ?1 and c.hotelId = ?2", req.getNetworkId(), hotelId);
        if (colrs == null) {
            lnew = true;
            colrs = new Colrs();
            colrs.setCrsno(req.getNetworkId());
            colrs.setOtano(req.getOtaOrderId());
            colrs.setChannel(req.getChannelCode());  //是否加对应的转换表
            colrs.setBookingid(seqNoService.getSequenceID(SystemUtil.SequenceKey.ORDERID));
            colrs.setHotelId(hotelId);
        }

        colrs.setTelephone(req.getPhone());
        colrs.setBookerName(req.getGuestInfo().getName());
        colrs.setArrivalDate(req.getArrDate());
        colrs.setDepartureDate(req.getDeptDate());

        if (lnew && req.getRooms().size() > 0) {
            //TODO 调用 coreRs 的批量创建订单
            //RsOrderResult result= coreRs.createBatchOrder(null);
        } else {//如果是编辑模式.就只保存综合预订接待单就好了

            daoLocal.merge(colrs);
        }

        CwCrsColRsSaveV1Res res = new CwCrsColRsSaveV1Res();
        return res;
    }

    public CwCrsRoomSaveV1Res saveCrsRoomRsV1(CwCrsRoomRsSaveV1Req req) {
        String hotelId = OtaGlobalContext.getCurrentHotelId();

        Colrs colrs = daoLocal.getObject("from Colrs c where c.crsno = ?1 and c.hotelId = ?2", req.getColligateNetworkId(), hotelId);
        if (colrs == null) {
            throw new RuntimeException("没有找到对应的订单");
        }

        boolean lnew = StrUtil.isNotBlank(req.getPmsId());
        Reservation reservation = null;
        if (lnew) {
            //创建一个新预订
            //TODO 调用 coreRs 的批量创建订单
        } else {
            reservation = daoLocal.getObject("from Reservation r where r.reservationNo = ?1 and r.hotelId = ?2", req.getPmsId(), hotelId);
            //TODO  参考 com.cw.service.config.reservation.impl.ReservationServiceImpl.updateReservation  更新客房预订
        }

        //新建或者编辑预订对象.参考

        CwCrsRoomSaveV1Res res = new CwCrsRoomSaveV1Res();
        return res;
    }

    public CwCrsColPayV1Res crsColPayV1(CwCrsColPayV1Req req) {
        String hotelId = OtaGlobalContext.getCurrentHotelId();
        //TODO 付款:
        //com.cw.service.config.ota.Pms_OtaServiceImpl.orderConsumption

        //TODO :  根据付款accid 找到 原来的付款入账. 入一笔负数的账目.进行对冲


        CwCrsColPayV1Res res = new CwCrsColPayV1Res();
        //

        return res;
    }


}
